2025-07-03 13:07:03 - devo_backup - INFO - Logging initialized. Log file: logs/devo_backup_20250703_130703.log
2025-07-03 13:07:04 - devo_backup - INFO - Specific date mode: Using date 2025-07-02
2025-07-03 13:07:04 - devo_backup - INFO - Executing specific date mode for: 2025-07-02
2025-07-03 13:07:04 - devo_backup - INFO - Devo SDK client initialized with endpoint: https://api-apac.devo.com/search/query
2025-07-03 13:07:04 - devo_backup - INFO - Starting backup for 1 date(s)
2025-07-03 13:07:04 - devo_backup - INFO - Testing connection to Devo API...
2025-07-03 13:07:04 - devo_backup - INFO - Testing connection to Devo API...
2025-07-03 13:07:04 - devo_backup - INFO - Executing Devo query: from siem.logtrust.web.activity select count() as test_count...
2025-07-03 13:07:04 - devo_backup - WARNING - Error processing query result: Bad parameters
2025-07-03 13:07:04 - devo_backup - INFO - Devo API connection test successful
2025-07-03 13:07:04 - devo_backup - INFO - Processing date 1/1: 2025-07-02
2025-07-03 13:07:04 - devo_backup - INFO - Pulling data for date: 2025-07-02 (4 tables)
2025-07-03 13:07:04 - devo_backup - INFO - Executing Devo query: from firewall.all...
2025-07-03 13:07:04 - devo_backup - WARNING - Error processing query result: Bad parameters
2025-07-03 13:07:04 - devo_backup - INFO - Data pull for 2025-07-02/firewall.all: SUCCESS - Records: 0
2025-07-03 13:07:05 - devo_backup - INFO - Executing Devo query: from siem.logtrust.web.activity...
2025-07-03 13:07:05 - devo_backup - WARNING - Error processing query result: Bad parameters
2025-07-03 13:07:05 - devo_backup - INFO - Data pull for 2025-07-02/siem.logtrust.web.activity: SUCCESS - Records: 0
2025-07-03 13:07:06 - devo_backup - INFO - Executing Devo query: from siem.logtrust.alert.info...
2025-07-03 13:07:06 - devo_backup - WARNING - Error processing query result: Bad parameters
2025-07-03 13:07:06 - devo_backup - INFO - Data pull for 2025-07-02/siem.logtrust.alert.info: SUCCESS - Records: 0
2025-07-03 13:07:07 - devo_backup - INFO - Executing Devo query: from my.app.data...
2025-07-03 13:07:07 - devo_backup - WARNING - Error processing query result: Bad parameters
2025-07-03 13:07:07 - devo_backup - INFO - Data pull for 2025-07-02/my.app.data: SUCCESS - Records: 0
2025-07-03 13:07:08 - devo_backup - INFO - Data pull for 2025-07-02: SUCCESS - Records: 0
2025-07-03 13:07:08 - devo_backup - INFO - Completed date 2025-07-02
2025-07-03 13:07:08 - devo_backup - INFO - ============================================================
2025-07-03 13:07:08 - devo_backup - INFO - BACKUP SUMMARY
2025-07-03 13:07:08 - devo_backup - INFO - ============================================================
2025-07-03 13:07:08 - devo_backup - INFO - Total dates processed: 1
2025-07-03 13:07:08 - devo_backup - INFO - Successful dates: 1
2025-07-03 13:07:08 - devo_backup - INFO - Failed dates: 0
2025-07-03 13:07:08 - devo_backup - INFO - Total records pulled: 0
2025-07-03 13:07:08 - devo_backup - INFO - Duration: 0:00:04.683589
2025-07-03 13:07:08 - devo_backup - INFO - Start time: 2025-07-03 13:07:04.169623
2025-07-03 13:07:08 - devo_backup - INFO - End time: 2025-07-03 13:07:08.853212
2025-07-03 13:07:08 - devo_backup - INFO - Backup completed successfully!
2025-07-03 13:07:08 - devo_backup - INFO - Backup Summary - Total: 1, Success: 1, Failed: 0
2025-07-03 13:07:08 - devo_backup - INFO - Results saved to: results/backup_results_20250703_130708.json
2025-07-03 13:07:08 - devo_backup - INFO - Backup system completed successfully

"""
Devo API client for data backup system using the official Devo SDK.
Handles authentication, rate limiting, retries, and robust error handling.
"""

import time
from typing import Dict, List, Optional, Any

# Import the official Devo SDK
try:
    from devo.api import Client, ClientConfig, SIMPLECOMPACT_TO_OBJ
except ImportError:
    raise ImportError("Devo SDK not found. Please install it with 'pip install devo-sdk'")

from config import config
from logger import logger
from date_utils import DateUtils


class DevoAPIError(Exception):
    """Custom exception for Devo API errors."""
    pass


class DevoClient:
    """Robust Devo API client using the official Devo SDK."""

    def __init__(self):
        """Initialize the Devo API client using the official SDK."""
        self.api_key = config.devo_api_key
        self.api_secret = config.devo_api_secret
        self.query_endpoint = config.devo_query_endpoint
        self.timeout = config.api_timeout
        self.max_retries = config.max_retries
        self.retry_delay = config.retry_delay
        self.rate_limit_delay = config.rate_limit_delay

        # Validate credentials
        if not self.api_key or not self.api_secret:
            raise DevoAPIError("Devo API credentials not found. Check your .env file.")

        if not self.query_endpoint:
            raise DevoAPIError("Devo query endpoint not found. Check your .env file.")

        # Initialize the Devo SDK client configuration
        self.client_config = ClientConfig(
            response="json/simple/compact",
            processor=SIMPLECOMPACT_TO_OBJ
        )

        # Initialize the Devo SDK client
        try:
            self.client = Client(
                auth={"key": self.api_key, "secret": self.api_secret},
                address=self.query_endpoint,
                config=self.client_config
            )
            logger.info(f"Devo SDK client initialized with endpoint: {self.query_endpoint}")
        except Exception as e:
            logger.error(f"Failed to initialize Devo SDK client: {str(e)}")
            raise DevoAPIError(f"Failed to initialize Devo SDK client: {str(e)}")
    
    def _execute_query_with_retry(self, query: str, start_time: str, end_time: str) -> Any:
        """
        Execute a query with retry logic using the Devo SDK.

        Args:
            query: Devo query string
            start_time: Start time in ISO format
            end_time: End time in ISO format

        Returns:
            Query results as dictionary

        Raises:
            DevoAPIError: If query fails after retries
        """
        for attempt in range(self.max_retries + 1):
            try:
                logger.debug(f"Executing query (attempt {attempt + 1}): {query[:100]}...")

                # Use the Devo SDK to execute the query
                result = self.client.query(
                    query=query,
                    dates={
                        "from": start_time,
                        "to": end_time
                    }
                )

                logger.debug(f"Query executed successfully on attempt {attempt + 1}")
                return result

            except Exception as e:
                error_msg = str(e)
                logger.warning(f"Query attempt {attempt + 1} failed: {error_msg}")

                # Check if it's a rate limiting error
                if "rate limit" in error_msg.lower() or "429" in error_msg:
                    sleep_time = self.rate_limit_delay * (attempt + 1)
                    logger.info(f"Rate limit detected, sleeping for {sleep_time} seconds")
                    time.sleep(sleep_time)
                elif attempt < self.max_retries:
                    sleep_time = self.retry_delay * (attempt + 1)
                    logger.info(f"Retrying in {sleep_time} seconds...")
                    time.sleep(sleep_time)
                else:
                    raise DevoAPIError(f"Query failed after {self.max_retries + 1} attempts: {error_msg}")

        raise DevoAPIError("Max retries exceeded")
    
    def query_data(self, query: str, start_time: str, end_time: str) -> Dict[str, Any]:
        """
        Execute a query against Devo API using the SDK.

        Args:
            query: Devo query string
            start_time: Start time in ISO format
            end_time: End time in ISO format

        Returns:
            Query results as dictionary

        Raises:
            DevoAPIError: If query fails
        """
        try:
            logger.info(f"Executing Devo query: {query[:100]}...")
            logger.debug(f"Query time range: {start_time} to {end_time}")

            # Use the SDK's query execution with retry logic
            result = self._execute_query_with_retry(query, start_time, end_time)

            # Convert the result to the expected format
            if hasattr(result, '__iter__') and not isinstance(result, (str, dict)):
                # If result is a generator or list, convert to dict format
                try:
                    data_list = []
                    for item in result:
                        data_list.append(item)
                        # Limit the number of items to prevent memory issues
                        if len(data_list) >= 10000:
                            logger.warning("Query returned more than 10,000 records, truncating...")
                            break

                    logger.info(f"Query returned {len(data_list)} records")
                    return {
                        "object": data_list,
                        "success": True,
                        "msg": "Query executed successfully"
                    }
                except Exception as e:
                    logger.warning(f"Error processing query result: {str(e)}")
                    # Return empty result if we can't process the generator
                    return {
                        "object": [],
                        "success": True,
                        "msg": f"Query executed but result processing failed: {str(e)}"
                    }
            elif isinstance(result, dict):
                return result
            else:
                # Wrap single results
                return {
                    "object": [result] if result is not None else [],
                    "success": True,
                    "msg": "Query executed successfully"
                }

        except Exception as e:
            logger.error(f"Query execution failed: {str(e)}")
            raise DevoAPIError(f"Query failed: {str(e)}")
    
    def get_available_tables(self) -> List[str]:
        """
        Get list of available tables from Devo.
        This is a placeholder - implement based on your Devo setup.
        
        Returns:
            List of table names
        """
        # This would typically be a separate API call to get table metadata
        # For now, return common Devo table patterns
        return [
            "firewall.all",
            "siem.logtrust.web.activity",
            "siem.logtrust.alert.info",
            "my.app.data"  # Replace with your actual tables
        ]
    
    def pull_data_for_date(self, date_str: str, tables: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Pull all data for a specific date.
        
        Args:
            date_str: Date in YYYY-MM-DD format
            tables: List of tables to query (if None, uses default tables)
            
        Returns:
            Dictionary with results for each table
        """
        if tables is None:
            tables = self.get_available_tables()
        
        start_time, end_time = DateUtils.format_date_for_query(date_str)
        results = {}
        
        logger.info(f"Pulling data for date: {date_str} ({len(tables)} tables)")
        
        for table in tables:
            try:
                # Basic query to get all data from table for the date
                query = f"from {table}"
                
                logger.debug(f"Querying table: {table}")
                table_data = self.query_data(query, start_time, end_time)
                
                # Count records
                record_count = len(table_data.get('object', []))
                results[table] = {
                    'data': table_data,
                    'record_count': record_count,
                    'success': True
                }
                
                logger.log_data_pull(f"{date_str}/{table}", record_count, True)
                
                # Rate limiting between table queries
                time.sleep(self.rate_limit_delay)
                
            except Exception as e:
                logger.error(f"Failed to pull data from table {table}: {str(e)}")
                results[table] = {
                    'data': None,
                    'record_count': 0,
                    'success': False,
                    'error': str(e)
                }
                logger.log_data_pull(f"{date_str}/{table}", 0, False)
        
        return results
    
    def test_connection(self) -> bool:
        """
        Test connection to Devo API using the SDK.

        Returns:
            True if connection successful, False otherwise
        """
        try:
            logger.info("Testing connection to Devo API...")

            # Simple test query
            current_date = DateUtils.get_current_date()
            start_time, end_time = DateUtils.format_date_for_query(current_date)

            # Use a simple query that should work on most Devo instances
            test_query = "from siem.logtrust.web.activity select count() as test_count"

            result = self.query_data(test_query, start_time, end_time)

            # Check if we got a valid result
            if result and (isinstance(result, dict) or hasattr(result, '__iter__')):
                logger.info("Devo API connection test successful")
                return True
            else:
                logger.warning("Devo API connection test returned empty result")
                return False

        except Exception as e:
            logger.error(f"Devo API connection test failed: {str(e)}")
            return False
